<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Recommendation scores
    |--------------------------------------------------------------------------
    |
    | These numbers are used to give books a score that decides what books get
    | recommended to the user.
    |
    */

    'scores' => [
        'similar_books' => [
            'author' => 8,
            'subject' => 10,
            'number_of_pages' => 1,
            'publication_date' => 1,
            'publisher' => 2,
        ],
        'recommendations_coefficients' => [
            'view' => 0.1,
            'finish' => 0.8,
            'like' => 1,
            'share' => 0.7,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Recommendations count
    |--------------------------------------------------------------------------
    |
    | The number of recommendations to generate per type.
    |
    */

    'count_per_type' => 8,

    /*
     |--------------------------------------------------------------------------
     | Books popularity parameters
     |--------------------------------------------------------------------------
     |
     | The global rating average and confidence threshold for the Bayesian average.
     |
     */

    'popularity' => [
        'google' => [
            'global_rating_average' => 3.5,
            'ratings_confidence_threshold' => 10,
        ],
        'open_library' => [
            'global_rating_average' => 3.5,
            'ratings_confidence_threshold' => 10,
            'max_reading_logs_count' => 146,
        ],
        'weights' => [
            'rating' => 0.7,
            'reading_logs' => 0.3,
        ],
    ],

];
