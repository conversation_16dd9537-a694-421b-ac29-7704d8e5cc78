<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Default language
    |--------------------------------------------------------------------------
    |
    | This language will be assigned to newly created users.
    |
    */

    'default_language' => 'en',

    /*
    |--------------------------------------------------------------------------
    | Default books
    |--------------------------------------------------------------------------
    |
    | These are the books that will be shown on the search page before performing a search. Might be cached in the
    | front-end.
    |
    */

    'default_books' => [
        'free' => [
            'b2e8ab11-0090-4961-94a5-d3a1423b5460',
            'e574b0d0-1919-41a8-9be7-9728d0eb484e',
            '482a2e78-c73f-4341-a030-22f5fd534165',
            '2071f812-3506-4ea3-ad31-63c4a1fdfe5c',
            '305e587f-4377-4f4d-aee0-9dade3dedcda',
            '9ba0be7c-bb86-424e-b5df-6a88945d6ebe',
            '9441c96c-ba1a-4b63-8008-319b9caff7be',
            '1e5de973-bdd8-47f3-80dd-468b08a90899',
            '1a7eb7e1-5803-404e-9102-831328387989',
            '0344c765-40aa-4567-b5ed-1f4fffcef10f',
            '33f3e154-4aa6-4254-bd3c-83718a5dd764',
            'be5379d5-9900-4706-b1af-20150e442665',
        ],
        'all' => [
            '70305d5a-ea16-4fcd-bb40-9183e7a92756',
            '50605ace-d7b3-42bc-a430-719cdd27f507',
            'e6bafe2a-67ee-4ade-9074-c62b72c8ec30',
            'b890a8de-09f6-42fa-99b5-eb694541cb38',
            'a3bf712c-b4d4-40e0-aae6-265e55ec58d1',
            'fd056dbd-dee7-4252-9a66-afe97ace592d',
            '298c840a-9fa4-47c2-8e69-1c5ee60158a8',
            '98362785-94bf-41c6-a7ca-b492abab7f7a',
            '68d1c6ef-ff68-4010-94f9-9195c5425190',
            '598deaff-4bfd-488f-9796-d84c9091252a',
            'ab292ae1-174e-46cf-8967-05bc0e85579d',
            'c63d8942-eae3-4c33-86d1-789c75d77a73',
        ],
    ],

    /*
     |--------------------------------------------------------------------------
     | Translation service
     |--------------------------------------------------------------------------
     |
     | This is the translation service whose API will be used to translate the
     | book descriptions and summaries.
     |
     | Supported: "deepl", "google", "aws", "azure"
     |
     */
    'translation_service' => env('TRANSLATION_SERVICE', 'deepl'),

    /*
     |--------------------------------------------------------------------------
     | User languages
     |--------------------------------------------------------------------------
     |
     | These are the languages that users can choose from in their account settings.
     |
     */
    'user_languages' => [
        'en',
        'es',
        'fr',
    ],

    /*
     |--------------------------------------------------------------------------
     | Python path
     |--------------------------------------------------------------------------
     |
     | This is the path to the Python executable that is used to run the TTS script.
     |
     */
    'tts_python_path' => env('TTS_PYTHON_PATH'),

    /*
     |--------------------------------------------------------------------------
     | TTS script path
     |--------------------------------------------------------------------------
     |
     | This is the path to the script that is used to generate the TTS audio files.
     |
     */
    'tts_script_path' => env('TTS_SCRIPT_PATH'),

    'quotes_scraper_python_path' => env('QUOTES_SCRAPER_PYTHON_PATH'),
    'quotes_scraper_script_path' => env('QUOTES_SCRAPER_SCRIPT_PATH'),

];
