<script lang="ts">
    import {PUBLIC_APP_NAME} from '$env/static/public';
    import FacebookSvg from '$lib/components/svg/FacebookSvg.svelte';
    import InstagramSvg from '$lib/components/svg/InstagramSvg.svelte';
    import XTheCompanySvg from '$lib/components/svg/XTheCompanySvg.svelte';
    import AppRoutes from '$routes/AppRoutes';

    const currentYear = new Date().getFullYear();
</script>

<svelte:head>
    <title>More - {PUBLIC_APP_NAME}</title>
</svelte:head>

<div class="max-w-2xl mx-auto">
    <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-8">More</h1>
    
    <!-- Social Media Links -->
    <div class="mb-8">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Follow Us</h2>
        <div class="space-y-4">
            <a
                href="https://instagram.com/liberomapp/"
                target="_blank"
                class="flex items-center space-x-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            >
                <InstagramSvg svgClass="w-6 h-6 text-pink-500" />
                <div>
                    <div class="font-medium text-gray-900 dark:text-white">Instagram</div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">@liberomapp</div>
                </div>
            </a>
            
            <a
                href="https://x.com/liberomapp"
                target="_blank"
                class="flex items-center space-x-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            >
                <XTheCompanySvg svgClass="w-6 h-6 text-gray-900 dark:text-white" />
                <div>
                    <div class="font-medium text-gray-900 dark:text-white">X (Twitter)</div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">@liberomapp</div>
                </div>
            </a>
            
            <a
                href="https://facebook.com/liberom.ap"
                target="_blank"
                class="flex items-center space-x-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            >
                <FacebookSvg svgClass="w-6 h-6 text-blue-600" />
                <div>
                    <div class="font-medium text-gray-900 dark:text-white">Facebook</div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">liberom.ap</div>
                </div>
            </a>
        </div>
    </div>

    <!-- Legal Links -->
    <div class="mb-8">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Legal</h2>
        <div class="space-y-3">
            <a
                href={AppRoutes.privacyPolicy}
                class="block p-4 bg-gray-50 dark:bg-gray-800 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            >
                <div class="font-medium text-gray-900 dark:text-white">Privacy Policy</div>
                <div class="text-sm text-gray-500 dark:text-gray-400">How we handle your data</div>
            </a>
            
            <a
                href={AppRoutes.termsOfService}
                class="block p-4 bg-gray-50 dark:bg-gray-800 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            >
                <div class="font-medium text-gray-900 dark:text-white">Terms of Service</div>
                <div class="text-sm text-gray-500 dark:text-gray-400">Terms and conditions</div>
            </a>
        </div>
    </div>

    <!-- App Information -->
    <div class="mb-8">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">About</h2>
        <div class="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div class="font-medium text-gray-900 dark:text-white mb-2">{PUBLIC_APP_NAME}</div>
            <div class="text-sm text-gray-500 dark:text-gray-400">
                Your digital library companion for discovering and enjoying books.
            </div>
        </div>
    </div>

    <!-- Copyright -->
    <div class="text-center py-8 border-t border-gray-200 dark:border-gray-700">
        <div class="text-sm text-gray-500 dark:text-gray-400">
            &#169; {currentYear} {PUBLIC_APP_NAME}. All rights reserved.
        </div>
    </div>
</div>
