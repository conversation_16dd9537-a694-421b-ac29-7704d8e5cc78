<script lang="ts">
    import {PUBLIC_APP_NAME} from '$env/static/public';
    import FacebookSvg from '$lib/components/svg/FacebookSvg.svelte';
    import InstagramSvg from '$lib/components/svg/InstagramSvg.svelte';
    import XSvg from '$lib/components/svg/XSvg.svelte';
    import XTheCompanySvg from '$lib/components/svg/XTheCompanySvg.svelte';
    import AppRoutes from '$routes/AppRoutes';

    export let isOpen: boolean = false;
    export let onClose: () => void;

    const currentYear = new Date().getFullYear();

    function handleBackdropClick(event: MouseEvent) {
        if (event.target === event.currentTarget) {
            onClose();
        }
    }

    function handleKeydown(event: KeyboardEvent) {
        if (event.key === 'Escape') {
            onClose();
        }
    }
</script>

<svelte:window on:keydown={handleKeydown} />

{#if isOpen}
    <!-- Backdrop -->
    <div
        class="fixed inset-0 z-50 bg-black bg-opacity-50 md:hidden"
        on:click={handleBackdropClick}
        role="button"
        tabindex="0"
        on:keydown={(e) => e.key === 'Enter' && onClose()}
    >
        <!-- Modal Content -->
        <div class="fixed bottom-0 left-0 right-0 bg-gray-50 dark:bg-gray-800 rounded-t-lg shadow-lg transform transition-transform duration-300 ease-out translate-y-0">
            <!-- Header -->
            <div class="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">More</h3>
                <button
                    type="button"
                    class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
                    on:click={onClose}
                >
                    <XSvg svgClass="w-6 h-6" />
                    <span class="sr-only">Close</span>
                </button>
            </div>

            <!-- Content -->
            <div class="p-6 pb-8 pb-safe">
                <!-- Social Media Links -->
                <div class="mb-6">
                    <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-3">Follow Us</h4>
                    <div class="flex items-center space-x-6">
                        <a
                            href="https://instagram.com/liberomapp/"
                            target="_blank"
                            class="flex items-center space-x-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
                        >
                            <InstagramSvg svgClass="w-5 h-5" />
                            <span class="text-sm">Instagram</span>
                        </a>
                        <a
                            href="https://x.com/liberomapp"
                            target="_blank"
                            class="flex items-center space-x-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
                        >
                            <XTheCompanySvg svgClass="w-5 h-5" />
                            <span class="text-sm">X</span>
                        </a>
                        <a
                            href="https://facebook.com/liberom.ap"
                            target="_blank"
                            class="flex items-center space-x-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
                        >
                            <FacebookSvg svgClass="w-5 h-5" />
                            <span class="text-sm">Facebook</span>
                        </a>
                    </div>
                </div>

                <!-- Legal Links -->
                <div class="mb-6">
                    <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-3">Legal</h4>
                    <div class="flex flex-col space-y-2">
                        <a
                            href={AppRoutes.privacyPolicy}
                            class="text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
                            on:click={onClose}
                        >
                            Privacy Policy
                        </a>
                        <a
                            href={AppRoutes.termsOfService}
                            class="text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
                            on:click={onClose}
                        >
                            Terms of Service
                        </a>
                    </div>
                </div>

                <!-- Copyright -->
                <div class="text-xs text-gray-400 dark:text-gray-500 text-center">
                    &#169; {currentYear} {PUBLIC_APP_NAME}
                </div>
            </div>
        </div>
    </div>
{/if}
