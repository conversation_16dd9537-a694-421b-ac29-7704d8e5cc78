<script lang="ts">
    import BookOpenSvg from '$lib/components/svg/BookOpenSvg.svelte';
    import ChatBubbleSvg from '$lib/components/svg/ChatBubbleSvg.svelte';
    import HamburgerSvg from '$lib/components/svg/HamburgerSvg.svelte';
    import HomeSvg from '$lib/components/svg/HomeSvg.svelte';
    import SearchSvg from '$lib/components/svg/SearchSvg.svelte';
    import {t} from '$lib/localization/Localization';
    import AppRoutes from '$routes/AppRoutes';
    import {page} from '$app/stores';

    export let onMoreClick: () => void;

    $: currentPath = $page.url.pathname;

    function isActive(route: string): boolean {
        if (route === AppRoutes.home) {
            return currentPath === route;
        }
        return currentPath.startsWith(route);
    }
</script>

<nav class="fixed bottom-0 left-0 right-0 z-50 bg-gray-50 border-t border-gray-200 dark:bg-gray-800 dark:border-gray-700 md:hidden safe-area-inset-bottom">
    <div class="flex items-center justify-around px-2 py-2 pb-safe">
        <!-- Home -->
        <a
            href={AppRoutes.home}
            class="flex flex-col items-center justify-center px-3 py-2 text-xs font-medium transition-colors duration-200"
            class:text-blue-600={isActive(AppRoutes.home)}
            class:dark:text-blue-400={isActive(AppRoutes.home)}
            class:text-gray-500={!isActive(AppRoutes.home)}
            class:dark:text-gray-400={!isActive(AppRoutes.home)}
            class:hover:text-gray-900={!isActive(AppRoutes.home)}
            class:dark:hover:text-white={!isActive(AppRoutes.home)}
        >
            <HomeSvg svgClass="w-6 h-6 mb-1" />
            <span>{$t('menu.home')}</span>
        </a>

        <!-- Search -->
        <a
            href={AppRoutes.search}
            class="flex flex-col items-center justify-center px-3 py-2 text-xs font-medium transition-colors duration-200"
            class:text-blue-600={isActive(AppRoutes.search)}
            class:dark:text-blue-400={isActive(AppRoutes.search)}
            class:text-gray-500={!isActive(AppRoutes.search)}
            class:dark:text-gray-400={!isActive(AppRoutes.search)}
            class:hover:text-gray-900={!isActive(AppRoutes.search)}
            class:dark:hover:text-white={!isActive(AppRoutes.search)}
        >
            <SearchSvg svgClass="w-6 h-6 mb-1" />
            <span>{$t('menu.search')}</span>
        </a>

        <!-- Recommendations -->
        <a
            href={AppRoutes.recommendations}
            class="flex flex-col items-center justify-center px-3 py-2 text-xs font-medium transition-colors duration-200"
            class:text-blue-600={isActive(AppRoutes.recommendations)}
            class:dark:text-blue-400={isActive(AppRoutes.recommendations)}
            class:text-gray-500={!isActive(AppRoutes.recommendations)}
            class:dark:text-gray-400={!isActive(AppRoutes.recommendations)}
            class:hover:text-gray-900={!isActive(AppRoutes.recommendations)}
            class:dark:hover:text-white={!isActive(AppRoutes.recommendations)}
        >
            <ChatBubbleSvg svgClass="w-6 h-6 mb-1" />
            <span>{$t('menu.recommendations')}</span>
        </a>

        <!-- My Library -->
        <a
            href={AppRoutes.myLibrary}
            class="flex flex-col items-center justify-center px-3 py-2 text-xs font-medium transition-colors duration-200"
            class:text-blue-600={isActive(AppRoutes.myLibrary)}
            class:dark:text-blue-400={isActive(AppRoutes.myLibrary)}
            class:text-gray-500={!isActive(AppRoutes.myLibrary)}
            class:dark:text-gray-400={!isActive(AppRoutes.myLibrary)}
            class:hover:text-gray-900={!isActive(AppRoutes.myLibrary)}
            class:dark:hover:text-white={!isActive(AppRoutes.myLibrary)}
        >
            <BookOpenSvg svgClass="w-6 h-6 mb-1" />
            <span>{$t('menu.myLibrary')}</span>
        </a>

        <!-- More -->
        <button
            type="button"
            class="flex flex-col items-center justify-center px-3 py-2 text-xs font-medium text-gray-500 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors duration-200"
            on:click={onMoreClick}
        >
            <HamburgerSvg svgClass="w-6 h-6 mb-1" />
            <span>More</span>
        </button>
    </div>
</nav>
