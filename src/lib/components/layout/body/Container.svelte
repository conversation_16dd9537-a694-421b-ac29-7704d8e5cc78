<script lang="ts">
    export let title = '';
    export let extraClass = '';
</script>

<div
    class={`container relative mx-auto mt-6 mb-3 space-y-4 rounded-lg bg-gray-50 p-4 shadow-md dark:bg-gray-800 lg:px-12 lg:py-6 ${extraClass}`}
>
    {#if title || $$slots.actions}
        <div class="flex justify-between">
            {#if title}
                <h1 class="text-2xl font-bold truncate">{title}</h1>
            {/if}
            <slot name="actions" />
        </div>
    {/if}
    <slot />
</div>
