<script lang="ts">
    import {PUBLIC_APP_NAME} from '$env/static/public';
    import Logo from '$lib/components/layout/header/Logo.svelte';
    import MenuItem from '$lib/components/layout/menu/MenuItem.svelte';
    import BookOpenSvg from '$lib/components/svg/BookOpenSvg.svelte';
    import ChatBubbleSvg from "$lib/components/svg/ChatBubbleSvg.svelte";
    import FacebookSvg from "$lib/components/svg/FacebookSvg.svelte";
    import HomeSvg from '$lib/components/svg/HomeSvg.svelte';
    import InstagramSvg from "$lib/components/svg/InstagramSvg.svelte";
    import SearchSvg from '$lib/components/svg/SearchSvg.svelte';
    import XTheCompanySvg from "$lib/components/svg/XTheCompanySvg.svelte";
    import {t} from '$lib/localization/Localization';
    import AppRoutes from '$routes/AppRoutes';

    const currentYear = new Date().getFullYear();
</script>

<aside
    id="drawer-navigation"
    class="fixed left-0 top-0 z-40 h-screen w-64 -translate-x-full border-r border-gray-200 bg-gray-50 pt-14 transition-transform dark:border-gray-700 dark:bg-gray-800 md:translate-x-0 hidden md:block"
    aria-label="Sidenav"
>
    <div class="h-full overflow-y-auto bg-gray-50 px-4 pt-5 dark:bg-gray-800">
        <div class="flex flex-col h-full">
            <div class="mb-5 sm:hidden">
                <Logo/>
            </div>
            <ul class="space-y-2 flex-1">
                <MenuItem title={$t('menu.home')} url={AppRoutes.home} iconComponent={HomeSvg}/>
                <MenuItem title={$t('menu.search')} url={AppRoutes.search} iconComponent={SearchSvg}/>
                <MenuItem
                    title={$t('menu.recommendations')}
                    url={AppRoutes.recommendations}
                    iconComponent={ChatBubbleSvg}
                />
                <MenuItem title={$t('menu.myLibrary')} url={AppRoutes.myLibrary} iconComponent={BookOpenSvg}/>
            </ul>
            <div class="text-xs text-center flex flex-wrap  justify-center border-t border-gray-200 px-4 py-2.5 text-gray-900 dark:border-gray-700  dark:text-white mt-auto">
                <div class="flex items-center mb-3 space-x-5">
                    <a href="https://instagram.com/liberomapp/" target="_blank">
                        <InstagramSvg svgClass="w-5 h-5"/>
                    </a>
                    <a href="https://x.com/liberomapp" target="_blank">
                        <XTheCompanySvg svgClass="w-5 h-5"/>
                    </a>
                    <a href="https://facebook.com/liberom.ap" target="_blank">
                        <FacebookSvg svgClass="w-5 h-5"/>
                    </a>
                </div>
                <div class="flex w-60 items-center space-x-5 px-2">
                    <a href={AppRoutes.privacyPolicy}>Privacy policy</a>
                    <a href={AppRoutes.termsOfService}>Terms of use</a>
                </div>
                <div class="flex items-center font-normal lg:order-2 text-gray-400">
                    &#169; {currentYear} {PUBLIC_APP_NAME}
                </div>
            </div>
        </div>
    </div>
</aside>
