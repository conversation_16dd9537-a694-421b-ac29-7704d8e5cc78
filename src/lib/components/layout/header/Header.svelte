<script lang="ts">
    import Logo from '$lib/components/layout/header/Logo.svelte';
    import ProfileLogin from '$lib/components/layout/header/ProfileLogin.svelte';
    import ProfileMenu from '$lib/components/layout/header/ProfileMenu.svelte';
    import ThemeToggle from '$lib/components/layout/header/ThemeToggle.svelte';
    import HamburgerSvg from '$lib/components/svg/HamburgerSvg.svelte';
    import ApiCallProgressStore from '$lib/stores/ApiCallProgressStore';
    import AuthenticatedStore from '$lib/stores/AuthenticatedStore';
</script>

<nav class="fixed left-0 right-0 top-0 z-50 border-b border-gray-200 bg-gray-50 dark:border-gray-700 dark:bg-gray-800">
    <div class="flex flex-wrap items-center px-4 py-2.5">
        <div class="flex items-center justify-start px-2 md:w-60">
            <!-- Hamburger button hidden on mobile since we use bottom navigation -->
            <button
                type="button"
                class="mr-2 cursor-pointer rounded-lg p-2 text-gray-600 hover:bg-gray-100 hover:text-gray-900 focus:bg-gray-100 focus:ring-2 focus:ring-gray-100 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:bg-gray-700 dark:focus:ring-gray-700 hidden"
                data-drawer-target="drawer-navigation"
                data-drawer-toggle="drawer-navigation"
                aria-controls="drawer-navigation"
            >
                <HamburgerSvg />
                <span class="sr-only">Toggle menu</span>
            </button>
            <div class="hidden sm:block">
                <Logo />
            </div>
        </div>
        <div class="flex items-center justify-start"></div>
        <div class="ml-auto flex items-center lg:order-2">
            <ProfileMenu />
            {#if !$AuthenticatedStore}
                <ProfileLogin />
            {/if}
        </div>
    </div>
    {#if $ApiCallProgressStore < 1}
        <progress
            class="absolute -bottom-0.5 bottom-0 h-0.5 w-full md:ml-64 md:w-[calc(100%-16rem)]"
            value={$ApiCallProgressStore}
        />
    {/if}
</nav>
