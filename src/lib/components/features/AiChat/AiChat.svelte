<script lang="ts">
    import {onMount, tick} from 'svelte';
    import AiConversationsApiClient from '$lib/api/AiConversationsApiClient';
    import {Button} from '$lib/components/shadcn/ui/button';
    import {Input} from '$lib/components/shadcn/ui/input';
    import {ScrollArea} from '$lib/components/shadcn/ui/scroll-area';
    import LoadingSpinner from '$lib/components/svg/LoadingSpinner.svelte';
    import type AiConversation from '$lib/domain/AiConversation';
    import {t} from '$lib/localization/Localization';

    export let conversations: AiConversation[];

    let activeConversationId = 0;
    let messages: AiConversation['messages'] = [];
    let inputMessage = '';
    let isLoading = false;
    let scrollAreaRef: HTMLDivElement;
    let showSidebar = true;
    let isMobile = false;
    let limitReached = false;
    let messageLimitReached = false;
    let inputContainerRef: HTMLDivElement;

    function initializeConversation() {
        if (conversations.length === 0) {
            createNewConversation();
            return;
        }

        selectConversation(conversations[0].id);
    }

    function checkMobile() {
        const mobileMaxWidth = 1024;
        isMobile = window.innerWidth < mobileMaxWidth;
        if (isMobile) {
            showSidebar = false;
        }
    }

    function createNewConversation() {
        const newChat = conversations.find((c) => c.id === 0);
        if (newChat !== undefined) {
            selectConversation(0);
            return;
        }

        const newConversation: AiConversation = {
            id: 0,
            title: $t('aiChat.newChat'),
            createdAt: $t('aiChat.now'),
            messages: [],
        };
        conversations = [newConversation, ...conversations];
        selectConversation(0);
    }

    async function selectConversation(conversationId: number) {
        activeConversationId = conversationId;
        const conversation: AiConversation | undefined = conversations.find((c) => c.id === conversationId);
        messages = conversation ? [...conversation.messages] : [];
        inputMessage = '';

        limitReached = false;
        messageLimitReached = false;

        if (isMobile) {
            showSidebar = false;
        }

        await scrollToBottom();
    }

    async function scrollToBottom() {
        // wait for the DOM to update so that the size of elements doesn't change after scrolling
        await tick();

        if (scrollAreaRef) {
            scrollAreaRef.lastElementChild?.scrollIntoView({behavior: 'auto', block: 'end'});
        }

        if (inputContainerRef) {
            const inputElement = inputContainerRef.querySelector('input');
            if (inputElement) {
                inputElement.focus();
            }
        }
    }

    function toggleSidebar() {
        showSidebar = !showSidebar;
    }

    function deleteConversation(conversationId: number) {
        conversations = conversations.filter((c) => c.id !== conversationId);
        if (activeConversationId === conversationId) {
            if (conversations.length > 0) {
                selectConversation(conversations[0].id);
            } else {
                createNewConversation();
            }
        }

        if (conversationId === 0) {
            return;
        }

        const aiConversationsApiClient = new AiConversationsApiClient();
        aiConversationsApiClient.deleteConversation(conversationId);
    }

    async function sendMessage() {
        if (inputMessage.trim() === '' || isLoading) {
            return;
        }

        const conversationIndex = conversations.findIndex((c) => c.id === activeConversationId);
        if (conversationIndex === -1) {
            return;
        }

        const userMessage = inputMessage.trim();
        inputMessage = '';
        isLoading = true;

        messages = [
            ...messages,
            {
                isSentByUser: true,
                content: userMessage,
            },
        ];
        conversations[conversationIndex].messages = messages;

        await scrollToBottom();

        const aiConversationsApiClient = new AiConversationsApiClient();
        const requestConversationId = activeConversationId === 0 ? null : activeConversationId;
        const response = await aiConversationsApiClient.sendMessage(userMessage, requestConversationId);

        if (!response.ok) {
            isLoading = false;

            return;
        }

        limitReached = response.data.limitReached;
        messageLimitReached = response.data.messageLimitReached;

        if (response.data.reply === '') {
            isLoading = false;
            await scrollToBottom();

            return;
        }

        messages = [
            ...messages,
            {
                isSentByUser: false,
                content: response.data.reply,
            } as AiConversation['messages'][0],
        ];

        conversations[conversationIndex].id = response.data.conversationId;
        activeConversationId = response.data.conversationId;
        conversations[conversationIndex].messages = [...messages];

        isLoading = false;
        await scrollToBottom();
    }

    onMount(() => {
        initializeConversation();

        checkMobile();
        window.addEventListener('resize', checkMobile);

        return () => {
            window.removeEventListener('resize', checkMobile);
        };
    });
</script>


<div class="mb-3 relative mx-auto mt-8 flex h-[calc(100vh-12rem)] md:h-[calc(100vh-8rem)]  w-full max-w-6xl overflow-hidden rounded-lg border">
    <div
        class="flex w-80 flex-shrink-0 flex-col dark:bg-gray-900 bg-white border-r bg-muted/30 transition-transform duration-300 ease-in-out md:relative md:translate-x-0"
        class:-translate-x-full={isMobile && !showSidebar}
        class:absolute={isMobile}
        class:inset-y-0={isMobile}
        class:left-0={isMobile}
        class:translate-x-0={isMobile && showSidebar}
        class:z-50={isMobile}
    >
        <div class="p-4">
            <Button class="w-full" on:click={createNewConversation}>
                <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                </svg>
                {$t('aiChat.newChat')}
            </Button>
        </div>
        <ScrollArea class="flex-1">
            <div class="conversation-list space-y-1 p-2">
                {#each conversations as conversation (conversation.id)}
                    <div
                        class="group relative cursor-pointer rounded-lg p-3 transition-colors hover:bg-muted/50"
                        class:bg-muted={activeConversationId === conversation.id}
                        role="button"
                        tabindex="0"
                        on:click={() => selectConversation(conversation.id)}
                        on:keydown={(event) => event.key === 'Enter' && selectConversation(conversation.id)}
                    >
                        <div class="flex items-start justify-between">
                            <div class="min-w-0 flex-1">
                                <h3 class="truncate text-sm font-medium">{conversation.title}</h3>
                                <p class="mt-1 text-xs text-muted-foreground">{conversation.createdAt}</p>
                            </div>
                            <button
                                type="button"
                                class="rounded p-1 opacity-0 transition-opacity hover:bg-destructive/10 group-hover:opacity-100"
                                title="Delete conversation"
                                on:click|stopPropagation={() => deleteConversation(conversation.id)}
                            >
                                <svg
                                    class="h-4 w-4 text-destructive"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                                    ></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                {/each}
            </div>
        </ScrollArea>
    </div>
    {#if isMobile && showSidebar}
        <div
            class="absolute inset-0 z-40 bg-black/50"
            role="button"
            tabindex="0"
            on:click={toggleSidebar}
            on:keydown={(event) => event.key === 'Escape' && toggleSidebar()}
        ></div>
    {/if}
    <div class="flex min-w-0 flex-1 flex-col">
        <div class="flex min-w-0 items-center border-b bg-background p-4">
            <div class="flex min-w-0 flex-1 items-center gap-3">
                {#if isMobile}
                    <Button variant="ghost" size="sm" on:click={toggleSidebar}>
                        <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                                d="M4 6h16M4 12h16M4 18h16"
                            ></path>
                        </svg>
                    </Button>
                {/if}
                <h2 class="min-w-0 max-w-full flex-1 truncate font-semibold">
                    {conversations.find((c) => c.id === activeConversationId)?.title}
                </h2>
            </div>
        </div>
        <div class="flex-1 overflow-hidden">
            <ScrollArea class="h-full p-4" scrollbarYClasses="chat-scrollbar">
                <div bind:this={scrollAreaRef} class="space-y-4">
                    <div class="flex items-start gap-4">
                        <div class="max-w-[70%] rounded-lg bg-muted p-3">
                            <p class="whitespace-pre-wrap">{$t('aiChat.introMessage')}</p>
                        </div>
                    </div>
                    {#each messages as message, index (index)}
                        {#if message.isSentByUser}
                            <div class="flex items-start justify-end gap-4">
                                <div class="max-w-[70%] rounded-lg bg-primary p-3 text-primary-foreground">
                                    <p class="whitespace-pre-wrap">{message.content}</p>
                                </div>
                            </div>
                        {:else}
                            <div class="flex items-start gap-4">
                                <div class="ai-message max-w-[70%] rounded-lg bg-muted p-3">
                                    <p class="whitespace-pre-wrap">{@html message.content}</p>
                                </div>
                            </div>
                        {/if}
                    {/each}
                    {#if isLoading}
                        <div class="flex items-start gap-4">
                            <div class="rounded-lg bg-muted p-3">
                                <LoadingSpinner />
                            </div>
                        </div>
                    {/if}
                    {#if limitReached}
                        <div class="flex items-start gap-4">
                            <div
                                class="max-w-[70%] rounded-lg border border-orange-200 bg-orange-50 p-3 text-orange-800 dark:border-orange-800 dark:bg-orange-950 dark:text-orange-200"
                            >
                                <div class="flex items-center gap-2">
                                    <svg
                                        class="h-5 w-5 text-orange-600 dark:text-orange-400"
                                        fill="none"
                                        stroke="currentColor"
                                        viewBox="0 0 24 24"
                                    >
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            stroke-width="2"
                                            d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                                        ></path>
                                    </svg>
                                    <p class="font-medium">{$t('aiChat.dailyLimitReached')}</p>
                                </div>
                                <p class="mt-1 text-sm">
                                    {$t('aiChat.dailyLimitReachedDescription')}
                                </p>
                            </div>
                        </div>
                    {/if}
                    {#if messageLimitReached}
                        <div class="flex items-start gap-4">
                            <div
                                class="max-w-[70%] rounded-lg border border-orange-200 bg-orange-50 p-3 text-orange-800 dark:border-orange-800 dark:bg-orange-950 dark:text-orange-200"
                            >
                                <div class="flex items-center gap-2">
                                    <svg
                                        class="h-5 w-5 text-orange-600 dark:text-orange-400"
                                        fill="none"
                                        stroke="currentColor"
                                        viewBox="0 0 24 24"
                                    >
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            stroke-width="2"
                                            d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                                        ></path>
                                    </svg>
                                    <p class="font-medium">{$t('aiChat.conversationLimitReached')}</p>
                                </div>
                                <p class="mt-1 text-sm">
                                    {$t('aiChat.conversationLimitReachedDescription')}
                                </p>
                            </div>
                        </div>
                    {/if}
                </div>
            </ScrollArea>
        </div>
        <div class="border-t bg-background p-4">
            <div bind:this={inputContainerRef} class="flex gap-2">
                <Input
                    class="flex-1"
                    bind:value={inputMessage}
                    placeholder={$t('aiChat.typeMessage')}
                    disabled={isLoading || limitReached || messageLimitReached}
                    on:keydown={(event) =>
                        event.key === 'Enter' && !event.shiftKey && (event.preventDefault(), sendMessage())}
                />
                <Button
                    disabled={isLoading || limitReached || messageLimitReached || inputMessage.trim() === ''}
                    on:click={sendMessage}
                >
                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"
                        ></path>
                    </svg>
                </Button>
            </div>
        </div>
    </div>
</div>

<style lang="css">
    :global(.chat-scrollbar) {
        visibility: visible !important;
    }

    :global(div:has(> .conversation-list)) {
        display: block !important;
    }

    :global(.ai-message a) {
        color: #99ccff;
        text-decoration: underline;
        font-weight: 500;
    }

    :global(.ai-message a:hover) {
        color: #80b2ff;
        text-decoration: underline;
    }

    @media (max-width: 1024px) {
        :global(.sidebar-mobile) {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            z-index: 50;
        }
    }
</style>
