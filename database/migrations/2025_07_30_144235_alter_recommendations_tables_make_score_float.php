<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('similar_books', function (Blueprint $table) {
            $table->float('points')->change();
        });
        Schema::table('similar_free_books', function (Blueprint $table) {
            $table->float('points')->change();
        });
        Schema::table('recommendations', function (Blueprint $table) {
            $table->float('score')->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('recommendations', function (Blueprint $table) {
            $table->integer('score')->change();
        });
        Schema::table('similar_free_books', function (Blueprint $table) {
            $table->integer('points')->change();
        });
        Schema::table('similar_books', function (Blueprint $table) {
            $table->integer('points')->change();
        });
    }
};
