<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('book_shares', function (Blueprint $table) {
            $table->boolean('with_quote')->default(false)->after('network');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('book_shares', function (Blueprint $table) {
            $table->dropColumn('with_quote');
        });
    }
};
