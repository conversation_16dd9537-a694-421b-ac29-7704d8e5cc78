<?php

use App\Models\Recommendation;
use App\Models\User;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Recommendation::whereNull('user_id')->delete();
        Schema::table('recommendations', function (Blueprint $table) {
            $table->foreignIdFor(User::class)->nullable(false)->change();
            $table->unique(['user_id', 'book_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('recommendations', function (Blueprint $table) {
            $table->foreignIdFor(User::class)->nullable()->change();
            $table->dropUnique(['user_id', 'book_id']);
        });
    }
};
