<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('recommendations', function (Blueprint $table) {
            $table->dropForeign(['recommendation_type_id']);
            $table->dropColumn('recommendation_type_id');
        });
        Schema::dropIfExists('recommendation_types');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::create('recommendation_types', function (Blueprint $table) {
            $table->id();
            $table->string('name');
        });
        Schema::table('recommendations', function (Blueprint $table) {
            $table->foreignId('recommendation_type_id')->constrained('recommendation_types');
        });
    }
};
