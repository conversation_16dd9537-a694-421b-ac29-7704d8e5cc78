<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('book_read_statuses', function (Blueprint $table) {
            $table->dropForeign(['book_read_status_type_id']);
            $table->dropColumn('book_read_status_type_id');
            $table->string('type')->after('id');
        });
        Schema::dropIfExists('book_read_status_types');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::create('book_read_status_types', function (Blueprint $table) {
            $table->id();
            $table->string('name');
        });
        Schema::table('book_read_statuses', function (Blueprint $table) {
            $table->dropColumn('type');
            $table->foreignId('book_read_status_type_id')->after('id')->constrained('book_read_status_types');
        });
    }
};
