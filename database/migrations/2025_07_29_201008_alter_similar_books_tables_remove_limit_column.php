<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('similar_books', function (Blueprint $table) {
            $table->dropColumn('books_compare_limit');
        });
        Schema::table('similar_free_books', function (Blueprint $table) {
            $table->dropColumn('books_compare_limit');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('similar_books', function (Blueprint $table) {
            $table->unsignedInteger('books_compare_limit')->default(10)->after('points');
        });

        Schema::table('similar_free_books', function (Blueprint $table) {
            $table->unsignedInteger('books_compare_limit')->default(10)->after('points');
        });
    }
};
