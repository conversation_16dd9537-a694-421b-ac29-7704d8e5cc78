<?php

use App\Models\Book;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('book_quotes', function (Blueprint $table) {
            $table->id();
            $table->text('content');
            $table->foreignIdFor(Book::class)->constrained('books');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('book_quotes');
    }
};
