<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Drop tables in correct order to handle foreign key constraints
        Schema::dropIfExists('audiobook_audiobook_subject');
        Schema::dropIfExists('audiobook_chapters');
        Schema::dropIfExists('audiobook_subjects');
        Schema::dropIfExists('audiobooks');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
    }
};
